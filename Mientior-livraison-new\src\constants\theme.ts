import { Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Palette de couleurs inspirée de l'Afrique
export const colors = {
  // Couleurs principales
  primary: {
    50: '#F0F9F3',
    100: '#DCF1E4',
    200: '#BCE3CB',
    300: '#8DCEA8',
    400: '#5BB47E',
    500: '#2E7D32', // Vert principal
    600: '#256C29',
    700: '#1E5720',
    800: '#1A4A1C',
    900: '#153E17',
  },

  // Couleurs secondaires (Orange/Rouge terracotta)
  secondary: {
    50: '#FFF4E6',
    100: '#FFE0B3',
    200: '#FFCC80',
    300: '#FFB74D',
    400: '#FFA726',
    500: '#FF9800', // Orange principal
    600: '#FB8C00',
    700: '#F57C00',
    800: '#EF6C00',
    900: '#E65100',
  },

  // Couleurs d'accent (Rouge africain)
  accent: {
    50: '#FFEBEE',
    100: '#FFCDD2',
    200: '#EF9A9A',
    300: '#E57373',
    400: '#EF5350',
    500: '#D32F2F', // Rouge principal
    600: '#C62828',
    700: '#B71C1C',
    800: '#AD1457',
    900: '#880E4F',
  },

  // Couleurs neutres
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },

  // Couleurs système
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  // Couleurs de statut spécifiques
  status: {
    pending: '#FFC107',
    confirmed: '#2196F3',
    preparing: '#FF9800',
    ready: '#9C27B0',
    inDelivery: '#3F51B5',
    delivered: '#4CAF50',
    cancelled: '#F44336',
  },

  // Arrière-plans
  background: {
    primary: '#FFFFFF',
    secondary: '#F8F9FA',
    tertiary: '#F5F5F5',
    overlay: 'rgba(0, 0, 0, 0.5)',
  },

  // Surfaces
  surface: {
    primary: '#FFFFFF',
    secondary: '#F8F9FA',
    elevated: '#FFFFFF',
  },

  // Bordures
  border: {
    light: '#E0E0E0',
    medium: '#BDBDBD',
    dark: '#757575',
  },

  // Texte
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#BDBDBD',
    inverse: '#FFFFFF',
  },
};

// Typographie
export const typography = {
  fontFamily: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
    light: 'System',
  },

  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },

  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
};

// Espacements
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
};

// Rayons de bordure
export const borderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 24,
  '2xl': 32,
  full: 9999,
};

// Ombres
export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 16,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 24,
  },
};

// Dimensions
export const dimensions = {
  screen: {
    width: screenWidth,
    height: screenHeight,
  },

  // Hauteurs communes
  headerHeight: 60,
  tabBarHeight: 70,
  statusBarHeight: 44, // iOS par défaut

  // Largeurs
  maxContentWidth: screenWidth > 768 ? 768 : screenWidth,

  // Hauteurs de composants
  buttonHeight: {
    sm: 32,
    base: 40,
    lg: 48,
    xl: 56,
  },

  inputHeight: {
    sm: 32,
    base: 40,
    lg: 48,
  },

  // Icônes
  iconSize: {
    xs: 12,
    sm: 16,
    base: 20,
    lg: 24,
    xl: 32,
    '2xl': 48,
  },
};

// Animations
export const animations = {
  timing: {
    fast: 150,
    normal: 300,
    slow: 500,
  },

  easing: {
    easeInOut: 'ease-in-out',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    linear: 'linear',
  },
};

// Breakpoints pour responsive design
export const breakpoints = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

// Thème par défaut avec couleurs simplifiées pour compatibilité
export const theme = {
  colors: {
    ...colors,
    // Ajout de couleurs simplifiées pour compatibilité
    primary: colors.primary[500],
    secondary: colors.secondary[500],
    accent: colors.accent[500],
    white: '#FFFFFF',
    black: '#000000',
  },
  typography,
  spacing,
  borderRadius,
  shadows,
  dimensions,
  animations,
  breakpoints,
};

// Mode sombre (pour implémentation future)
export const darkTheme = {
  ...theme,
  colors: {
    ...colors,
    primary: {
      ...colors.primary,
      500: '#4CAF50', // Vert plus clair pour le mode sombre
    },
    background: {
      primary: '#121212',
      secondary: '#1E1E1E',
      tertiary: '#2C2C2C',
      overlay: 'rgba(255, 255, 255, 0.1)',
    },
    surface: {
      primary: '#1E1E1E',
      secondary: '#2C2C2C',
      elevated: '#2C2C2C',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#BDBDBD',
      disabled: '#757575',
      inverse: '#000000',
    },
  },
};

// Utilitaires de thème
export const themeUtils = {
  // Obtenir une couleur avec opacité
  withOpacity: (color: string, opacity: number) => {
    // Convertir hex en rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  },

  // Calculer un padding responsive
  responsivePadding: (base: number) => {
    return screenWidth > breakpoints.md ? base * 1.5 : base;
  },

  // Calculer une taille de police responsive
  responsiveFontSize: (base: number) => {
    return screenWidth > breakpoints.md ? base * 1.1 : base;
  },

  // Vérifier si c'est un grand écran
  isLargeScreen: () => screenWidth > breakpoints.md,

  // Obtenir la hauteur disponible (sans header/tabs)
  getAvailableHeight: () => {
    return screenHeight - dimensions.headerHeight - dimensions.tabBarHeight;
  },
};

export default theme;