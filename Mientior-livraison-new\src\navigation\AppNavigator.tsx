import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuthStore } from '../store/authStore';
import { RootStackParamList } from '../types';

// Écrans d'authentification et onboarding
import OnboardingScreen from '../screens/onboarding/OnboardingScreen';
import AuthScreen from '../screens/auth/AuthScreen';
import RoleSelectionScreen from '../screens/auth/RoleSelectionScreen';
import SupabaseTestScreen from '../screens/SupabaseTestScreen';

// Navigateurs par rôle
import ClientNavigator from './ClientNavigator';
import DeliveryNavigator from './DeliveryNavigator';
import MerchantNavigator from './MerchantNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();

  // Si l'utilisateur n'est pas authentifié
  if (!isAuthenticated || !user) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="Onboarding" component={OnboardingScreen} />
        <Stack.Screen name="Auth" component={AuthScreen} />
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur est authentifié mais n'a pas de rôle défini
  if (!user.role) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Navigateur selon le rôle utilisateur
  const getRoleNavigator = () => {
    switch (user.role) {
      case 'client':
        return ClientNavigator;
      case 'livreur':
        return DeliveryNavigator;
      case 'marchand':
        return MerchantNavigator;
      default:
        return ClientNavigator; // Par défaut, interface client
    }
  };

  const RoleNavigator = getRoleNavigator();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen name="Main" component={RoleNavigator} />
      <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
