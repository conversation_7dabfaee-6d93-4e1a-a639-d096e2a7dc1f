import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useAuthStore } from '../../store/authStore';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../types';
import { theme } from '../../constants/theme';

type AuthScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Auth'>;

type Props = {
  navigation: AuthScreenNavigationProp;
};

const AuthScreen: React.FC<Props> = ({ navigation }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const { signIn, signUp, loading: authLoading } = useAuthStore();

  // Validation des champs
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
  };

  const validatePassword = (password: string) => {
    return password.length >= 6;
  };

  const handleAuth = async () => {
    try {
      setError('');
      setLoading(true);

      // Validation des champs
      if (!email || !password) {
        setError('Veuillez remplir tous les champs obligatoires');
        return;
      }

      if (!validateEmail(email)) {
        setError('Veuillez entrer une adresse email valide');
        return;
      }

      if (!validatePassword(password)) {
        setError('Le mot de passe doit contenir au moins 6 caractères');
        return;
      }

      if (isLogin) {
        await signIn(email, password);
      } else {
        // Validation supplémentaire pour l'inscription
        if (!name || !phone) {
          setError('Veuillez remplir tous les champs');
          return;
        }

        if (!validatePhone(phone)) {
          setError('Veuillez entrer un numéro de téléphone valide');
          return;
        }

        await signUp(email, password, {
          full_name: name,
          phone: phone,
          role: 'client'
        });

        // Naviguer vers la vérification OTP
        navigation.navigate('OTPVerification', {
          phone: phone,
          email: email
        });
      }
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      setError('Veuillez entrer votre adresse email');
      return;
    }

    if (!validateEmail(email)) {
      setError('Veuillez entrer une adresse email valide');
      return;
    }

    Alert.alert(
      'Réinitialisation du mot de passe',
      'Un email de réinitialisation sera envoyé à votre adresse.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Envoyer',
          onPress: async () => {
            try {
              setLoading(true);
              // TODO: Implémenter avec le service d'authentification
              // await authService.resetPassword(email);
              Alert.alert(
                'Email envoyé',
                'Un lien de réinitialisation a été envoyé à votre adresse email.'
              );
            } catch (error: any) {
              Alert.alert('Erreur', error.message || 'Impossible d\'envoyer l\'email.');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header avec logo */}
        <View style={styles.header}>
          <Image
            source={require('../../../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.title}>
            {isLogin ? 'Bon retour !' : 'Créer un compte'}
          </Text>
          <Text style={styles.subtitle}>
            {isLogin
              ? 'Connectez-vous pour continuer'
              : 'Rejoignez la communauté Mientior'}
          </Text>
        </View>

        {/* Formulaire */}
        <View style={styles.form}>
          {error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.error}>⚠️ {error}</Text>
            </View>
          ) : null}

          {!isLogin && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Nom complet *</Text>
              <TextInput
                style={[styles.input, !name && styles.inputError]}
                placeholder="Votre nom complet"
                value={name}
                onChangeText={setName}
                autoCapitalize="words"
                editable={!loading && !authLoading}
              />
            </View>
          )}

          {!isLogin && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Téléphone *</Text>
              <TextInput
                style={[styles.input, !phone && styles.inputError]}
                placeholder="+225 XX XX XX XX XX"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                editable={!loading && !authLoading}
              />
            </View>
          )}

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Email *</Text>
            <TextInput
              style={[styles.input, !validateEmail(email) && email && styles.inputError]}
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!loading && !authLoading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Mot de passe *</Text>
            <TextInput
              style={[styles.input, !validatePassword(password) && password && styles.inputError]}
              placeholder="Minimum 6 caractères"
              secureTextEntry
              value={password}
              onChangeText={setPassword}
              editable={!loading && !authLoading}
            />
            {!isLogin && password && (
              <Text style={[
                styles.passwordStrength,
                { color: validatePassword(password) ? theme.colors.success : theme.colors.error }
              ]}>
                {validatePassword(password) ? '✓ Mot de passe valide' : '✗ Minimum 6 caractères'}
              </Text>
            )}
          </View>

          {/* Bouton principal */}
          <TouchableOpacity
            style={[
              styles.button,
              (loading || authLoading) && styles.buttonDisabled
            ]}
            onPress={handleAuth}
            disabled={loading || authLoading}
          >
            {loading || authLoading ? (
              <ActivityIndicator color={theme.colors.text.inverse} size="small" />
            ) : (
              <Text style={styles.buttonText}>
                {isLogin ? 'Se connecter' : 'S\'inscrire'}
              </Text>
            )}
          </TouchableOpacity>

          {/* Mot de passe oublié */}
          {isLogin && (
            <TouchableOpacity
              style={styles.forgotPasswordButton}
              onPress={handleForgotPassword}
              disabled={loading || authLoading}
            >
              <Text style={styles.forgotPasswordText}>
                Mot de passe oublié ?
              </Text>
            </TouchableOpacity>
          )}

          {/* Basculer entre connexion et inscription */}
          <TouchableOpacity
            style={styles.switchButton}
            onPress={() => {
              setIsLogin(!isLogin);
              setError('');
            }}
            disabled={loading || authLoading}
          >
            <Text style={styles.switchText}>
              {isLogin
                ? 'Pas encore de compte ? '
                : 'Déjà un compte ? '}
              <Text style={styles.switchTextBold}>
                {isLogin ? 'Inscrivez-vous' : 'Connectez-vous'}
              </Text>
            </Text>
          </TouchableOpacity>

          {/* Connexion sociale (placeholder) */}
          {isLogin && (
            <View style={styles.socialContainer}>
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>ou</Text>
                <View style={styles.dividerLine} />
              </View>

              <TouchableOpacity
                style={styles.socialButton}
                disabled={true} // Désactivé pour l'instant
              >
                <Text style={styles.socialButtonText}>
                  🔜 Connexion avec Google (bientôt)
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
  },
  errorContainer: {
    backgroundColor: theme.colors.error + '15',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
  },
  error: {
    color: theme.colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 8,
  },
  input: {
    backgroundColor: theme.colors.surface.primary,
    borderWidth: 1,
    borderColor: theme.colors.border.light,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: theme.colors.text.primary,
    ...theme.shadows.sm,
  },
  inputError: {
    borderColor: theme.colors.error,
    borderWidth: 2,
  },
  passwordStrength: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  button: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    ...theme.shadows.base,
  },
  buttonDisabled: {
    backgroundColor: theme.colors.neutral[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonText: {
    color: theme.colors.text.inverse,
    fontSize: 18,
    fontWeight: '600',
  },
  forgotPasswordButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  forgotPasswordText: {
    color: theme.colors.primary[500],
    fontSize: 14,
    fontWeight: '500',
  },
  switchButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 20,
  },
  switchText: {
    color: theme.colors.text.secondary,
    fontSize: 14,
    textAlign: 'center',
  },
  switchTextBold: {
    color: theme.colors.primary[500],
    fontWeight: '600',
  },
  socialContainer: {
    marginTop: 32,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.border.light,
  },
  dividerText: {
    marginHorizontal: 16,
    color: theme.colors.text.secondary,
    fontSize: 14,
  },
  socialButton: {
    backgroundColor: theme.colors.surface.secondary,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border.light,
  },
  socialButtonText: {
    color: theme.colors.text.secondary,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AuthScreen;
